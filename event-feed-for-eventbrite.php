<?php
//removeIf(!premium)
/**
 * Plugin Name:       Event Feed for Eventbrite Pro
 * Plugin URI:        https://www.eventfeed.click/
 * Description:       The easiest way to publish Eventbrite events to your WordPress site.
 * Version:           1.3.3
 * Author:            Bohemia Plugins
 * Author URI:        https://www.bohemiaplugins.com/
 * License:           GPL-3.0+
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       event-feed-for-eventbrite
 * Domain Path:       /languages
 * 
 * @fs_ignore event-feed-for-eventbrite.php, index.php, /admin/, /freemius/, /includes/, /languages/, /public/
 * 
 * Event Feed for Eventbrite, 
 * Copyright (C) 2021, Bohemia Plugins, <EMAIL>
 * 
 * Event Feed for Eventbrite is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * Event Feed for Eventbrite is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Event Feed for Eventbrite. If not, see <https://www.gnu.org/licenses/>.
 * 
 * Event Feed for Eventbrite incorporates code from WordPress plugin "Display Eventbrite Events in WordPress"
 * <https://wordpress.org/plugins/widget-for-eventbrite-api/> by Fullworks <https://fullworks.net/>.
 * License: GNU GPL, Version 2
 * 
 * Event Feed for Eventbrite also incorporates code from discontinued WordPress plugin "Eventbrite API"
 * <https://github.com/Automattic/eventbrite-api> by Automattic <https://automattic.com>.
 * License: GNU GPL, Version 2 or newer
 * 
 * Event Feed for Eventbrite uses graphic assets from "Lineicons Free Basic" icon pack <https://lineicons.com/icons/>
 * by Lineicons <https://lineicons.com/>.
 * License: Attribution-ShareAlike 4.0 International (CC BY-SA 4.0)
 * <https://creativecommons.org/licenses/by-sa/4.0/legalcode>
 */
//endRemoveIf(!premium)
//removeIf(premium)
/**
 * Plugin Name:       Event Feed for Eventbrite
 * Plugin URI:        https://www.eventfeed.click/
 * Description:       The easiest way to publish Eventbrite events to your WordPress site.
 * Version:           1.3.3
 * Author:            Bohemia Plugins
 * Author URI:        https://www.bohemiaplugins.com/
 * License:           GPL-3.0+
 * License URI:       https://www.gnu.org/licenses/gpl-3.0.txt
 * Text Domain:       event-feed-for-eventbrite
 * Domain Path:       /languages
 * 
 * Event Feed for Eventbrite, 
 * Copyright (C) 2021, Bohemia Plugins, <EMAIL>
 * 
 * Event Feed for Eventbrite is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * any later version.
 *
 * Event Feed for Eventbrite is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with Event Feed for Eventbrite. If not, see <https://www.gnu.org/licenses/>.
 * 
 * Event Feed for Eventbrite incorporates code from WordPress plugin "Display Eventbrite Events in WordPress"
 * <https://wordpress.org/plugins/widget-for-eventbrite-api/> by Fullworks <https://fullworks.net/>.
 * License: GNU GPL, Version 2
 * 
 * Event Feed for Eventbrite also incorporates code from discontinued WordPress plugin "Eventbrite API"
 * <https://github.com/Automattic/eventbrite-api> by Automattic <https://automattic.com>.
 * License: GNU GPL, Version 2 or newer
 * 
 * Event Feed for Eventbrite uses graphic assets from "Lineicons Free Basic" icon pack <https://lineicons.com/icons/>
 * by Lineicons <https://lineicons.com/>.
 * License: Attribution-ShareAlike 4.0 International (CC BY-SA 4.0)
 * <https://creativecommons.org/licenses/by-sa/4.0/legalcode>
 */
//endRemoveIf(premium)
namespace EFFE_Plugin;

if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

if ( function_exists( 'EFFE_Plugin\effe_freemius' ) ) {

    //removeIf(!premium)
    effe_freemius()->set_basename( true, __FILE__ );
    //endRemoveIf(!premium)
    //removeIf(premium)
    effe_freemius()->set_basename( false, __FILE__ );
    //endRemoveIf(premium)

} else {

    if ( ! function_exists( 'EFFE_Plugin\effe_freemius' ) ) {

        /**
         * Main constants.
         */
        define( 'EFFE_PLUGIN_NAME', 'event-feed-for-eventbrite' );
        define( 'EFFE_VERSION', '1.3.3' );

        /**
         * Freemius SDK
         */
        function effe_freemius() {

            global $effe_freemius;

            if ( ! isset( $effe_freemius ) ) {

                // Include Freemius SDK.
                require_once dirname(__FILE__) . '/freemius/start.php';
                
                $effe_freemius = fs_dynamic_init( array(
                    'id'                  => '8768',
                    'slug'                => 'event-feed-for-eventbrite',
                    'type'                => 'plugin',
                    'public_key'          => 'pk_556f1acb2efa40434636e6f468b09',
                    //removeIf(!premium)
                    'is_premium'          => true,
                    //endRemoveIf(!premium)
                    //removeIf(premium)
                    // 'is_premium'          => false,
                    //endRemoveIf(premium)
                    'premium_suffix'      => 'Pro',
                    'has_premium_version' => true,
                    'has_addons'          => false,
                    'has_paid_plans'      => true,
                    'trial'               => array(
                        'days'               	=> 14,
                        'is_require_payment' 	=> true,
                    ),
                    'menu'                => array(
                        'slug'           		=> 'edit.php?post_type=event_feed',
                        'first-path'            => 'edit.php?post_type=event_feed&page=event-feed-for-eventbrite-getting-started',
                        'contact'        		=> true,
                        'support'				=> false
                    ),
                    //removeIf(production)
                    'secret_key'          => 'sk_Ex&;-60Dn[v_$IBLBBNx1x!R.8OZc',
                    //endRemoveIf(production)
                    //removeIf(!production)
                    'is_live'        => true,
                    //endRemoveIf(!production)
                ) );
            }

            return $effe_freemius;
        }

        // Init Freemius.
        effe_freemius();

        // Signal that SDK was initiated.
        do_action( 'effe_freemius_loaded' );

    }

    /**
     * The code that runs during plugin activation.
     */
    register_activation_hook( __FILE__, function() {
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-activator.php';
        Event_Feed_For_Eventbrite_Activator::activate();
    } );

    /**
     * The code that runs during plugin deactivation.
     */
    register_deactivation_hook( __FILE__, function() {
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-deactivator.php';
        Event_Feed_For_Eventbrite_Deactivator::deactivate();
    } );

    /**
     * The code that runs after plugin uninstall.
     */
    effe_freemius()->add_action( 'after_uninstall', function() {
        require_once plugin_dir_path( __FILE__ ) . 'includes/class-uninstaller.php';
        Event_Feed_For_Eventbrite_Uninstaller::uninstall();
    } );

    /**
     * The core plugin class that is used to define internationalization,
     * admin-specific hooks, and public-facing site hooks.
     */
    require plugin_dir_path( __FILE__ ) . 'includes/class-event-feed-for-eventbrite.php';

    /**
     * Begins execution of the plugin.
     *
     * @since    1.0.0
     */
    function run_event_feed_for_eventbrite() {

        $plugin = new Event_Feed_For_Eventbrite();
        $plugin->run();

    }
    run_event_feed_for_eventbrite();

}