=== Show Eventbrite Events - Event Feed for Eventbrite ===
Contributors: bohemiaplugins, jan<PERSON><PERSON><PERSON>
Donate link: https://www.eventfeed.click/pricing/
Tags: eventbrite, events, widget, calendar, api
Requires at least: 5.0
Tested up to: 6.7
Stable tag: 1.3.3
Requires PHP: 5.6
Type: freemium
License: GPLv3 or later
License URI: http://www.gnu.org/licenses/gpl-3.0.html

Show Eventbrite events easily with the Eventbrite WordPress plugin. Eventbrite widget integration without imports or complicated setup. No coding needed.

== Description ==

🚀 No imports or complicated setup. Show Eventbrite events easily and attractively with just a few clicks.

📅 [Try Demo](https://www.eventfeed.click/demo/) | ⚡ [Pro Version](https://www.eventfeed.click/pricing/) | 📃 [Check Documentation](https://www.eventfeed.click/docs/)

### 💎 The most user-friendly Eventbrite plugin ###

[Event Feed for Eventbrite](https://www.eventfeed.click/) takes events from your Eventbrite organization and lets you display them on your WordPress website in various ways. Need a list of upcoming events? Choose from [List](https://www.eventfeed.click/demo/list/), [Grid](https://www.eventfeed.click/demo/grid/), or [Cards](https://www.eventfeed.click/demo/cards/) layout. Want to show your events as a [Widget](https://www.eventfeed.click/demo/widget/)? No problem, we got you covered.

**Customize from WordPress Dashboard** – Customize event feed's [layout](https://www.eventfeed.click/docs/choosing-layout/), [color](https://www.eventfeed.click/docs/customizing-color/), [font family](https://www.eventfeed.click/docs/changing-font/), and [displayed information](https://www.eventfeed.click/docs/displayed-information/) to ensure it perfectly matches your website design. Control how your events are [showing on mobile devices](https://www.eventfeed.click/docs/responsive-settings/) without the need to touch the code.

**Integrated Ticket Checkout** – [Ticket Checkout](https://www.eventfeed.click/docs/ticket-checkout/) seamlessly integrated into a popup lets users get their tickets from Eventbrite without leaving your website. [Pro version](https://www.eventfeed.click/pricing/) goes even further, [showing all necessary event details in a popup](https://www.eventfeed.click/docs/event-details-popup/), so there is absolutely no need for a user to navigate away to the Eventbrite website.

**Manage events on Eventbrite** – The plugin is reading data directly from Eventbrite, which means less work from your side. You can manage your events on Eventbrite, how you are used to, and all the changes made there will be automatically synced to your event feeds.

### 🎉 Free Features ###
* Display an unlimited number of event feeds
* Display events in [List](https://www.eventfeed.click/demo/list/) or [Widget](https://www.eventfeed.click/demo/widget/) layout
* [Customize the color](https://www.eventfeed.click/docs/customizing-color/) of the event feed
* [Limit number of displayed events](https://www.eventfeed.click/docs/responsive-settings/) in the event feed (separately for a mobile, small tablet, large tablet, and desktop)
* [Control which event details you want to show/hide](https://www.eventfeed.click/docs/displayed-information/) - show title, image, date & time, price, location, description, and more
* [Limit event title length](https://www.eventfeed.click/docs/displayed-information/#trimming-long-titles-or-descriptions)
* [Limit event description length](https://www.eventfeed.click/docs/displayed-information/#trimming-long-titles-or-descriptions)
* [Change the text](https://www.eventfeed.click/docs/displayed-information/#customizing-buttons) of *Buy tickets*/*Sign up*, and *View details* buttons
* Open external links in the same/new window
* Open [Ticket Checkout](https://www.eventfeed.click/docs/ticket-checkout/) in a popup/on the Eventbrite website
* [Choose Eventbrite organization](https://www.eventfeed.click/docs/eventbrite-settings/) from whose events are loaded
* [Add a unique CSS ID](https://www.eventfeed.click/docs/styling-with-css/#adding-custom-selectors) to the event feed
* [Add CSS classes](https://www.eventfeed.click/docs/styling-with-css/#adding-custom-selectors) to the event feed
* [Cache-control for increased performance](https://www.eventfeed.click/docs/eventbrite-cache/) (specify cache period; you can use the button in the admin bar to manually clear the cache)
* [Image placeholder](https://www.eventfeed.click/docs/placeholder-image/) (used when the event has no image on Eventbrite)
* [Option to use Google Fonts](https://www.eventfeed.click/docs/changing-font/) instead of the theme's default font
* [Set event date and time format](https://www.eventfeed.click/docs/date-and-time-format/)
* [Set event address format](https://www.eventfeed.click/docs/address-format/)

### ⚡ PRO Features ###
* Display events in [Grid](https://www.eventfeed.click/demo/grid/) or [Cards](https://www.eventfeed.click/demo/cards/) layout
* [Specify the number of columns](https://www.eventfeed.click/docs/responsive-settings/) (separately for a mobile, small tablet, large tablet, and desktop breakpoints)
* [Display Event Details in a popup](https://www.eventfeed.click/docs/event-details-popup/)
  - [Fully formatted event description](https://www.eventfeed.click/docs/event-details-popup/#full-event-description), including images and videos
  - [Add to calendar button](https://www.eventfeed.click/docs/event-details-popup/#add-to-calendar-button) (import to Google Calendar, Outlook, Office365, Yahoo or download ICS file)
  - [Event location map](https://www.eventfeed.click/docs/event-details-popup/#google-map) using Google Maps
  - [Event organizer information](https://www.eventfeed.click/docs/event-details-popup/#event-organizer-information) (photo, name, description, social links, website link)
  - [Link to Eventbrite](https://www.eventfeed.click/docs/event-details-popup/#link-to-eventbrite) (useful if you are using the popup solution but still want a user to have the option to go to Eventbrite)
  - Shareable popup URLs – each popup has a unique URL, making it easy to share event details with others
* [Time Filter](https://www.eventfeed.click/docs/event-filters/#time-filter) - display future/past events
* [Privacy Filter](https://www.eventfeed.click/docs/event-filters/#privacy-filter) - display only public/private events
* [Venue Filter](https://www.eventfeed.click/docs/event-filters/#venue-filter) - display only online events/events with the venue
* [Name Filter](https://www.eventfeed.click/docs/event-filters/#name-filter) - display only events whose name contains specific text
* [Location Filter](https://www.eventfeed.click/docs/event-filters/#location-filter) - display only events whose location contains specific text
* [Description Filter](https://www.eventfeed.click/docs/event-filters/#description-filter) - display only events whose description contains specific text
* [Organizer Filter](https://www.eventfeed.click/docs/event-filters/#organizer-filter) - display only events organized by a specific organizer

== Installation ==

From within WordPress' dashboard:

1. Go to **Plugins** -> **Add New**
2. Search for **Event Feed for Eventbrite**
3. Click **Install**
4. Click **Activate**
5. Enter [your Eventbrite API key](https://www.eventbrite.com/platform/api-keys).

== Screenshots ==

1. You can use the List layout in any area on your website with enough horizontal space.
2. Widget layout is optimized for a sidebar or area with limited horizontal space.
3. Grid layout can fit any place on your website thanks to its high flexibility. You can use it in full-width areas, areas with limited horizontal space, as well as in the sidebar.
4. Cards layout is also highly flexible and works really well when placed on a 'non-white background' (the white cards will stand out).
5. Installation wizard to help you get your Eventbrite token.
6. Managing event feeds is similar to managing regular WordPress posts. 
7. Event Feed Settings. 
8. You can change your Eventbrite token in the Settings. You can also specify for how long your events will be cached.
9. If you are not happy with the font provided by your theme, you can use any other font from Google fonts. You can also set the image placeholder for your events.
10. Choose the date and time format and set the address format for your Eventbrite events. 
11. Miscellaneous Settings.

== Frequently Asked Questions ==

= Can I have more than one event feed on my website? =

You can publish an unlimited number of event feeds with any number of events in both the Free and Pro version of the plugin. 

You can also display more than one event feed on the same page.

= What is the difference between the Free and Pro version? =

With the Free version, you can use the [List](https://www.eventfeed.click/demo/list/) and [Widget](https://www.eventfeed.click/demo/widget/) layout. With the [Pro version](https://www.eventfeed.click/pricing/), you get access to two additional layouts - [Grid](https://www.eventfeed.click/demo/grid/) and [Cards](https://www.eventfeed.click/demo/cards/).

[Ticket Checkout](https://www.eventfeed.click/docs/ticket-checkout/) embedded into a popup works in both Free and Pro versions. However, you need to upgrade to the [Pro version](https://www.eventfeed.click/pricing/) if you want to show the event details in the popup.

[Event Filters](https://www.eventfeed.click/docs/event-filters/) are another feature only available in the [Pro version](https://www.eventfeed.click/pricing/). Except that, everything works in a Free version without any limits.

= How do I connect the plugin to Eventbrite? =

Navigate to this link or copy this URL to your web browser:
[https://www.eventbrite.com/platform/api-keys](https://www.eventbrite.com/platform/api-keys)

If you are not logged to Eventbrite, click on the *Get a Free API Key* button and log in.
    
When logged in, you should now be able to see your private token. Copy it to the clipboard by clicking on the *Copy* button.

= Where can I find instructions on how to use the plugin? =

You can find the [detailed documentation here](https://www.eventfeed.click/docs/).

= Events are not displaying or are outdated =

Our plugin uses caching to speed up loading of the events for the user. That means that if you have published your events on Eventbrite after setting up the event feed on your website, there is a chance you see the cached state of Eventbrite when there were no events published. You can manually clear the cache from the WordPress admin bar - [see the article in our documentation](https://www.eventfeed.click/docs/eventbrite-cache/) for more details.

= The date and time are not the same as on Eventbrite. =

This is caused by a different timezone setting on Eventbrite and your WordPress website. If you want to respect the Eventbrite timezone, go to Eventbrite Events -> Settings -> Date & address tab and turn on the Eventbrite Timezone option.

= Why doesn't my event feed look the same as on the demo? =

Sometimes your WordPress theme can overwrite the styling of the event feed we bundled with the plugin. Luckily, this is usually easy to fix. If this has happened to you, please let us know — we will look at that for you and provide a fix.

If you use the Free version of the plugin, you can get help on the [plugin's WordPress.org forum](https://wordpress.org/support/plugin/event-feed-for-eventbrite/). If you are the [Pro version](https://www.eventfeed.click/pricing/) user, please use [Priority Support on our official website](https://www.eventfeed.click/support/).

= Ticket Checkout is not opening on my website. How to fix that? =

You need a valid SSL certificate for the [Ticket Checkout](https://www.eventfeed.click/docs/ticket-checkout/) to work correctly in the popup. If your website address begins with *https://*, your SSL certificate is active, and you are good to go. However, if it starts with *http://*, we recommend contacting your web hosting company and getting a fresh SSL certificate. 

It also applies to local development. Ticket Checkout Popup will only work when accessing the website using the secure HTTPS protocol.
    
If you can't get the SSL certificate to work, don't worry, you can still use the plugin. Just change the Ticket Checkout behavior to *Link to Eventbrite*. After clicking on the *Buy button*, visitors will be redirected to the Eventbrite website instead and be able to get the tickets there.

= I am still stuck. Where can I get help or ask a question? =

If you use the Free version of the plugin, you can get help on the [plugin's WordPress.org forum](https://wordpress.org/support/plugin/event-feed-for-eventbrite/). If you are the [Pro version](https://www.eventfeed.click/pricing/) user, please use [Priority Support on our official website](https://www.eventfeed.click/support/).

== Changelog ==

= 1.3.2 =
* Tweak - Updated Freemius SDK to the latest version

= 1.3.2 =
* Tweak - Updated Freemius SDK to the latest version

= 1.3.1 =
* Feature - Added unique URL for each popup, you can now share a link to a popup with others

= 1.3.0 =
* Feature - Added WPML compatibility - event feeds are now translatable
* Feature - Better compatibility with WP Rocket and SiteGround Optimizer
* Tweak - Added explicit width and height to main images to improve Cumulative Layout Shift (CLS) metric

= 1.2.5 =
* Fix - Corrected price formatting to display two decimal places
* Tweak - Improved debugging
* Security - Improved security

= 1.2.4 =
* Fix - Remove deprecation warnings when PHP 8.2 or higher is used

= 1.2.3 =
* Feature - Added option to show months instead of days in short time (on event card)
* Tweak - Google Fonts list updated 

= 1.2.2 =
* Feature - Added Description Filter

= 1.2.1 =
* Fix - Compatibility with Siteground Optimizer

= 1.2.0 =
* Feature - Added Organizer Filter
* Tweak - Code improvements on admin screens
* Fix - Datetime is now localized when used in combination with Eventbrite timezone

= 1.1.3 =
* Fix - Compatibility with WordPress 6.5

= 1.1.2 =
* Security - Improved security

= 1.1.1 =
* Fix - Compatibility with WordPress 6.2

= 1.1.0 =
* Feature - Added option to respect Eventbrite timezone (instead of following WordPress timezone settings)
* Feature - Added option to display remaining tickets
* Tweak - Make the option to respect the Eventbrite timezone a default option for new plugin activations
* Tweak - Updated list of supported Google Fonts
* Tweak - Improved popup styles on Mac
* Fix - Name and location filter bug fix for PHP 8.0 and PHP 8.1
* Fix - ICS file download fixed in Chrome (Mac)

= 1.0.8 =
* Feature - Plugin is now compatible with Full site editing introduced in WordPress 5.9
* Feature - Added time filter allowing to display past events
* Tweak - Increased z-index of the popup (decreasing the risk that other elements on the page will be overflowing the popup)

= 1.0.7 =
* Security - Improved security

= 1.0.6 =
* Fix - Fixes the bug causing the "No upcoming events..." message to appear in the Pro version

= 1.0.5 =
* Fix - Admin columns styles are now loaded only on the relevant admin page
* Fix - Improved responsive styles both for front-end and back-end

= 1.0.4 =
* Tweak - Buttons on Cards Layout are now aligned to the end of card
* Tweak - Added better reset styles (to avoid some themes overriding event feed styles)
* Tweak - Improved internationalization
* Fix - Date is now displayed in the language which is set sitewide in the WordPress admin
* Fix - Fixed problem of custom date format not being saved to database when set for the first time

= 1.0.3 =
* Feature - The event organizer info box now contains links to Facebook, Twitter, and the organizer's website
* Tweak - Improved buttons accessibility
* Tweak - Improved links and buttons hover effects
* Tweak - Improved event description formatting
* Tweak - Event organizer description now shows rich formatting
* Tweak - Images and videos in the event description now use the full width

= 1.0.2 =
* Security - Better data sanitizing

= 1.0.1 =
* Fix - Eventbrite trademark removed

= 1.0 =
* Initial release